{"system": [{"name": "show_setup_wizard", "value": true}, {"name": "direction", "value": "ltr", "is_public": true}, {"name": "locale", "value": "en", "is_public": true}, {"name": "timezone", "value": "Africa/Lagos", "is_public": true}, {"name": "color_scheme", "value": "default", "is_public": true}, {"name": "date_format", "value": "MMMM D, YYYY", "is_public": true}, {"name": "time_format", "value": "h:mm A", "is_public": true}, {"name": "per_page", "value": 10, "is_public": true}, {"name": "currency", "value": "NGN", "is_public": true}, {"name": "currencies", "value": "NGN", "is_public": true}, {"name": "footer_credit", "value": "© 2025 eedu.tech by Qwiksales. All rights reserved.", "is_public": true}, {"name": "show_version_number", "value": false, "is_public": true}, {"name": "enable_dark_theme", "value": false, "is_public": true}, {"name": "enable_mini_sidebar", "value": true}, {"name": "enable_maintenance_mode", "value": false}, {"name": "enable_setup_wizard", "value": true}, {"name": "enable_print_preview", "value": true, "is_public": true}, {"name": "enable_strong_password", "value": true, "is_public": true}], "feature": [{"name": "enable_todo", "value": true}, {"name": "enable_activity_log", "value": true}, {"name": "enable_online_registration", "value": true, "is_public": true}, {"name": "online_registration_instruction", "value": "", "is_public": true, "is_html": true}, {"name": "enable_guest_payment", "value": true, "is_public": true}, {"name": "guest_payment_instruction", "value": "", "is_public": true, "is_html": true}, {"name": "enable_job_application", "value": true, "is_public": true}, {"name": "job_application_instruction", "value": "", "is_public": true, "is_html": true}], "notification": [{"name": "enable_mail_notification", "value": true, "is_public": true}, {"name": "enable_mobile_push_notification", "value": false, "is_public": false}, {"name": "enable_guest_notification_bar", "value": false, "is_public": true}, {"name": "enable_app_notification_bar", "value": false}, {"name": "guest_notification_message", "value": "", "is_public": true}, {"name": "app_notification_message", "value": ""}, {"name": "enable_pusher_notification", "value": false, "is_public": true}, {"name": "pusher_app_id", "value": "", "is_secret": true}, {"name": "pusher_app_key", "value": "", "is_public": true}, {"name": "pusher_app_secret", "value": "", "is_secret": true}, {"name": "pusher_app_cluster", "value": "", "is_public": true}], "general": [{"name": "app_name", "value": "eedu.tech", "is_public": true}, {"name": "meta_author", "value": "Qwi<PERSON><PERSON>", "is_public": true}, {"name": "meta_description", "value": "Application by Qwiksales", "is_public": true}, {"name": "meta_keywords", "value": "Qwi<PERSON><PERSON>", "is_public": true}], "auth": [{"name": "session_lifetime", "value": 1440}, {"name": "login_throttle_max_attempts", "value": 5}, {"name": "login_throttle_lock_timeout", "value": 2}, {"name": "enable_reset_password", "value": true, "is_public": true}, {"name": "reset_password_token_lifetime", "value": 30}, {"name": "enable_registration", "value": false, "is_public": false}, {"name": "enable_registration_terms", "value": false, "is_public": false}, {"name": "enable_email_verification", "value": false, "is_public": false}, {"name": "enable_account_approval", "value": true}, {"name": "enable_two_factor_security", "value": false}, {"name": "enable_oauth_login", "value": false, "is_public": true}, {"name": "google_client_id", "is_secret": true}, {"name": "google_client_secret", "is_secret": true}, {"name": "facebook_client_id", "is_secret": true}, {"name": "facebook_client_secret", "is_secret": true}, {"name": "twitter_client_id", "is_secret": true}, {"name": "twitter_client_secret", "is_secret": true}, {"name": "github_client_id", "is_secret": true}, {"name": "github_client_secret", "is_secret": true}, {"name": "microsoft_client_id", "is_secret": true}, {"name": "microsoft_client_secret", "is_secret": true}, {"name": "enable_otp_login", "value": true, "is_public": true}, {"name": "otp_login_lifetime", "value": 10}, {"name": "enable_email_otp_login", "value": true, "is_public": true}, {"name": "enable_sms_otp_login", "value": true, "is_public": true}, {"name": "enable_screen_lock", "value": false}, {"name": "screen_lock_timeout", "value": 10}], "assets": [{"name": "favicon", "value": "/images/favicon.png", "is_public": true}, {"name": "logo", "value": "/images/logo.png", "is_public": true}, {"name": "logo_light", "value": "/images/logo-light.png", "is_public": true}, {"name": "icon", "value": "/images/icon.png", "is_public": true}, {"name": "icon_light", "value": "/images/icon-light.png", "is_public": true}, {"name": "guest_background", "value": "/images/guest-background.webp", "is_public": true}], "mail": [{"name": "driver", "value": "log"}, {"name": "from_name", "value": "eedu.tech"}, {"name": "from_address", "value": "<EMAIL>"}, {"name": "smtp_username", "value": "", "is_secret": true}, {"name": "smtp_password", "value": "", "is_secret": true}, {"name": "mailgun_domain", "value": "", "is_secret": true}, {"name": "mailgun_secret", "value": "", "is_secret": true}, {"name": "mailgun_endpoint", "value": "", "is_secret": false}], "sms": [{"name": "driver", "value": null, "is_secret": false}, {"name": "max_sms_per_queue", "value": 100, "is_secret": false}, {"name": "sender_id", "value": null, "is_secret": false}, {"name": "test_number", "value": null, "is_secret": false}, {"name": "api_key", "value": null, "is_secret": true}, {"name": "api_secret", "value": null, "is_secret": true}, {"name": "api_url", "value": null, "is_secret": false}, {"name": "api_method", "value": "GET", "is_secret": false}, {"name": "number_prefix", "value": null, "is_secret": false}, {"name": "sender_id_param", "value": null, "is_secret": false}, {"name": "receiver_param", "value": null, "is_secret": false}, {"name": "message_param", "value": null, "is_secret": false}, {"name": "template_id_param", "value": null, "is_secret": false}, {"name": "api_headers", "value": null, "is_secret": true}, {"name": "additional_params", "value": null, "is_secret": true}], "social_network": [], "utility": [{"name": "todo_view", "value": "board"}], "chat": [{"name": "enable_chat", "value": true}, {"name": "enable_technical_support_chat", "value": true}, {"name": "enable_technical_support_for_students_guardians", "value": false}, {"name": "technical_support_chat_url", "value": "https://support.eedu.tech"}, {"name": "technical_support_chat_token", "value": "gyHk1zb2Wwo6XYNdSbMWGQc1"}], "site": [{"name": "enable_site", "value": false}, {"name": "show_public_view", "value": false}, {"name": "theme", "value": "default", "is_public": true}, {"name": "color_scheme", "value": "default", "is_public": true}], "blog": [{"name": "enable_blog", "value": true}], "contact": [{"name": "enable_middle_name_field", "value": false}, {"name": "enable_third_name_field", "value": false}, {"name": "unique_id_number1_label", "value": "Unique ID Number 1", "is_public": false}, {"name": "unique_id_number2_label", "value": "Unique ID Number 2", "is_public": false}, {"name": "unique_id_number3_label", "value": "Unique ID Number 3", "is_public": false}, {"name": "is_unique_id_number1_required", "value": false}, {"name": "is_unique_id_number2_required", "value": false}, {"name": "is_unique_id_number3_required", "value": false}], "academic": [{"name": "period_selection", "value": "period_wise"}, {"name": "allow_listing_subject_wise_student", "value": true}], "student": [{"name": "registration_number_prefix", "value": "SM"}, {"name": "registration_number_digit", "value": 3}, {"name": "registration_number_suffix", "value": ""}, {"name": "admission_number_prefix", "value": "SM"}, {"name": "admission_number_digit", "value": 3}, {"name": "admission_number_suffix", "value": ""}, {"name": "transfer_request_number_prefix", "value": "TR"}, {"name": "transfer_request_number_digit", "value": 3}, {"name": "transfer_request_number_suffix", "value": ""}, {"name": "transfer_number_prefix", "value": "T"}, {"name": "transfer_number_digit", "value": 3}, {"name": "transfer_number_suffix", "value": ""}, {"name": "attendance_past_day_limit", "value": 7}, {"name": "allow_student_to_submit_contact_edit_request", "value": true}, {"name": "late_fee_waiver_till_date", "value": ""}, {"name": "allow_flexible_installment_payment", "value": false}], "employee": [{"name": "code_number_prefix", "value": "ESM", "is_public": false}, {"name": "code_number_digit", "value": 3, "is_public": false}, {"name": "code_number_suffix", "value": "", "is_public": false}, {"name": "unique_id_number1_label", "value": "Unique ID Number 1", "is_public": false}, {"name": "unique_id_number2_label", "value": "Unique ID Number 2", "is_public": false}, {"name": "unique_id_number3_label", "value": "Unique ID Number 3", "is_public": false}, {"name": "is_unique_id_number1_required", "value": false, "is_public": false}, {"name": "is_unique_id_number2_required", "value": false, "is_public": false}, {"name": "is_unique_id_number3_required", "value": false, "is_public": false}, {"name": "allow_employee_to_submit_contact_edit_request", "value": true}, {"name": "allow_employee_request_leave_with_exhausted_credit", "value": false}, {"name": "allow_employee_clock_in_out", "value": true, "is_public": false}, {"name": "enable_qr_code_attendance", "value": true, "is_public": false}, {"name": "use_dynamic_qr_code", "value": false, "is_public": false}, {"name": "duration_between_clock_request", "value": 5, "is_public": false}, {"name": "allow_employee_clock_in_out_via_device", "value": true, "is_public": true}, {"name": "late_grace_period", "value": 15, "is_public": false}, {"name": "early_leaving_grace_period", "value": 15, "is_public": false}, {"name": "present_grace_period", "value": 30, "is_public": false}, {"name": "enable_geolocation_timesheet", "value": false, "is_public": false}, {"name": "geolocation_latitude", "value": "", "is_public": false}, {"name": "geolocation_longitude", "value": "", "is_public": false}, {"name": "cache_geolocation_data", "value": 10000}, {"name": "geolocation_radius", "value": 100, "is_public": false}, {"name": "payroll_number_prefix", "value": "PSM", "is_public": false}, {"name": "payroll_number_digit", "value": 3, "is_public": false}, {"name": "payroll_number_suffix", "value": "", "is_public": false}], "finance": [{"name": "enable_bank_code1", "value": true, "is_public": false}, {"name": "bank_code1_label", "value": "Bank Code 1", "is_public": false}, {"name": "is_bank_code1_required", "value": false, "is_public": false}, {"name": "enable_bank_code2", "value": true, "is_public": false}, {"name": "bank_code2_label", "value": "Bank Code 2", "is_public": false}, {"name": "is_bank_code2_required", "value": false, "is_public": false}, {"name": "enable_bank_code3", "value": true, "is_public": false}, {"name": "bank_code3_label", "value": "Bank Code 3", "is_public": false}, {"name": "is_bank_code3_required", "value": false, "is_public": false}, {"name": "payment_number_prefix", "value": "TP%YEAR_SHORT%%MONTH_NUMBER_SHORT%", "is_public": false}, {"name": "payment_number_digit", "value": 3}, {"name": "payment_number_suffix", "value": ""}, {"name": "receipt_number_prefix", "value": "TR%YEAR_SHORT%%MONTH_NUMBER_SHORT%", "is_public": false}, {"name": "receipt_number_digit", "value": 3}, {"name": "receipt_number_suffix", "value": ""}, {"name": "transfer_number_prefix", "value": "TT%YEAR_SHORT%%MONTH_NUMBER_SHORT%", "is_public": false}, {"name": "transfer_number_digit", "value": 3}, {"name": "transfer_number_suffix", "value": ""}, {"name": "bank", "value": ""}, {"name": "bank_code", "value": ""}, {"name": "account_number", "value": ""}, {"name": "account_name", "value": ""}, {"name": "account_email", "value": ""}, {"name": "client_id", "value": ""}, {"name": "client", "value": ""}, {"name": "account_id", "value": ""}, {"name": "vfd_bvn", "value": "", "is_public": false, "is_secret": true}, {"name": "enable_online_transaction_number", "value": false}, {"name": "online_transaction_number_prefix", "value": "OT%YEAR_SHORT%%MONTH_NUMBER_SHORT%", "is_public": false}, {"name": "online_transaction_number_digit", "value": 3, "is_public": false}, {"name": "online_transaction_number_suffix", "value": "", "is_public": false}, {"name": "enable_paypal", "value": false}, {"name": "enable_live_paypal_mode", "value": false}, {"name": "paypal_client", "value": "", "is_secret": true}, {"name": "paypal_secret", "value": "", "is_secret": true}, {"name": "enable_stripe", "value": false}, {"name": "enable_live_stripe_mode", "value": false}, {"name": "stripe_client", "value": "", "is_secret": true}, {"name": "stripe_secret", "value": "", "is_secret": true}, {"name": "enable_payzone", "value": false}, {"name": "enable_live_payzone_mode", "value": false}, {"name": "payzone_merchant", "value": ""}, {"name": "payzone_secret_key", "value": "", "is_secret": true}, {"name": "payzone_notification_key", "value": "", "is_secret": true}, {"name": "enable_vfd", "value": false}, {"name": "enable_live_vfd_mode", "value": false}, {"name": "enable_vfd_parent_bears_charge", "value": false}, {"name": "vfd_consumer_key", "value": "", "is_secret": true}, {"name": "vfd_consumer_secret", "value": "", "is_secret": true}, {"name": "enable_razorpay", "value": false}, {"name": "enable_live_razorpay_mode", "value": false}, {"name": "razorpay_client", "value": "", "is_secret": true}, {"name": "razorpay_secret", "value": "", "is_secret": true}, {"name": "enable_paystack", "value": true}, {"name": "enable_live_paystack_mode", "value": true}, {"name": "paystack_client", "value": "pk_test_3ffb1e05c5d7acc64854ba20d14e6544d6ed1b3b", "is_secret": true}, {"name": "paystack_secret", "value": "sk_test_568241f52e795c9670f55d4b9bb0bdced44a440d", "is_secret": true}, {"name": "enable_ccavenue", "value": false}, {"name": "enable_live_ccavenue_mode", "value": false}, {"name": "ccavenue_merchant", "value": "", "is_secret": false}, {"name": "ccavenue_client", "value": "", "is_secret": true}, {"name": "ccavenue_secret", "value": "", "is_secret": true}, {"name": "enable_billdesk", "value": false, "is_public": true}, {"name": "billdesk_version", "value": "1.2", "is_public": true}, {"name": "enable_live_billdesk_mode", "value": false, "is_public": true}, {"name": "billdesk_merchant", "value": "", "is_secret": false}, {"name": "billdesk_client", "value": "", "is_secret": true}, {"name": "billdesk_secret", "value": "", "is_secret": true}], "reception": [{"name": "enquiry_number_prefix", "value": "RESM", "is_public": false}, {"name": "enquiry_number_digit", "value": 3, "is_public": false}, {"name": "enquiry_number_suffix", "value": "", "is_public": false}, {"name": "visitor_log_number_prefix", "value": "RVLSM", "is_public": false}, {"name": "visitor_log_number_digit", "value": 3, "is_public": false}, {"name": "visitor_log_number_suffix", "value": "", "is_public": false}, {"name": "gate_pass_number_prefix", "value": "RGPSM", "is_public": false}, {"name": "gate_pass_number_digit", "value": 3, "is_public": false}, {"name": "gate_pass_number_suffix", "value": "", "is_public": false}, {"name": "complaint_number_prefix", "value": "RCSM", "is_public": false}, {"name": "complaint_number_digit", "value": 3, "is_public": false}, {"name": "complaint_number_suffix", "value": "", "is_public": false}, {"name": "query_number_prefix", "value": "RQSM", "is_public": false}, {"name": "query_number_digit", "value": 3, "is_public": false}, {"name": "query_number_suffix", "value": "", "is_public": false}], "exam": [{"name": "marksheet_format", "value": "School"}, {"name": "visible_marksheet_types", "value": ["cumulative", "term_wise", "exam_wise_default"]}, {"name": "show_in_report_sheet", "value": []}, {"name": "comment_label", "value": ""}, {"name": "comment_behavioural_label", "value": ""}], "resource": [{"name": "allow_edit_diary_by_accessible_user", "value": false}, {"name": "allow_delete_diary_by_accessible_user", "value": false}, {"name": "allow_edit_syllabus_by_accessible_user", "value": false}, {"name": "allow_delete_syllabus_by_accessible_user", "value": false}, {"name": "allow_edit_lesson_plan_by_accessible_user", "value": false}, {"name": "allow_delete_lesson_plan_by_accessible_user", "value": false}, {"name": "online_class_use_meeting_code", "value": true}, {"name": "online_class_joining_period", "value": 10}, {"name": "allow_edit_online_class_by_accessible_user", "value": false}, {"name": "allow_delete_online_class_by_accessible_user", "value": false}, {"name": "allow_edit_assignment_by_accessible_user", "value": false}, {"name": "allow_delete_assignment_by_accessible_user", "value": false}, {"name": "allow_edit_learning_material_by_accessible_user", "value": false}, {"name": "allow_delete_learning_material_by_accessible_user", "value": false}, {"name": "enable_filter_by_assigned_subject", "value": false}], "calendar": [{"name": "show_celebration_in_dashboard", "value": true}, {"name": "event_number_prefix", "value": "CESM", "is_public": false}, {"name": "event_number_digit", "value": 3, "is_public": false}, {"name": "event_number_suffix", "value": "", "is_public": false}], "inventory": [{"name": "stock_requisition_number_prefix", "value": "ISRSM", "is_public": false}, {"name": "stock_requisition_number_digit", "value": 3, "is_public": false}, {"name": "stock_requisition_number_suffix", "value": "", "is_public": false}, {"name": "stock_purchase_number_prefix", "value": "ISPSM", "is_public": false}, {"name": "stock_purchase_number_digit", "value": 3, "is_public": false}, {"name": "stock_purchase_number_suffix", "value": "", "is_public": false}, {"name": "stock_transfer_number_prefix", "value": "ISTSM", "is_public": false}, {"name": "stock_transfer_number_digit", "value": 3, "is_public": false}, {"name": "stock_transfer_number_suffix", "value": "", "is_public": false}, {"name": "stock_adjustment_number_prefix", "value": "ISASM", "is_public": false}, {"name": "stock_adjustment_number_digit", "value": 3, "is_public": false}, {"name": "stock_adjustment_number_suffix", "value": "", "is_public": false}], "communication": [{"name": "announcement_number_prefix", "value": "CASM", "is_public": false}, {"name": "announcement_number_digit", "value": 3, "is_public": false}, {"name": "announcement_number_suffix", "value": "", "is_public": false}], "recruitment": [{"name": "vacancy_number_prefix", "value": "SMV"}, {"name": "vacancy_number_digit", "value": 3}, {"name": "vacancy_number_suffix", "value": ""}], "transport": [{"name": "show_transport_route_in_dashboard", "value": true}], "mess": [{"name": "show_mess_schedule_in_dashboard", "value": true}], "gallery": [{"name": "show_gallery_in_dashboard", "value": true}, {"name": "enable_watermark", "value": true}, {"name": "watermark_position", "value": "top-right"}, {"name": "watermark_size", "value": 40}], "ai": [{"name": "enable_ai", "value": false}, {"name": "enable_assistant", "value": true}, {"name": "enable_academic_chat", "value": true}, {"name": "enable_voice_input", "value": false}, {"name": "enable_voice_output", "value": false}, {"name": "default_provider", "value": ""}, {"name": "enable_fallback", "value": true}, {"name": "enable_rate_limit", "value": false}, {"name": "rate_limit_requests_per_minute", "value": 60}, {"name": "rate_limit_requests_per_hour", "value": 1000}, {"name": "rate_limit_requests_per_day", "value": 10000}, {"name": "auto_approve_knowledge", "value": false}, {"name": "is_knowledge_public_by_default", "value": false}, {"name": "enable_conversation_saving", "value": true}, {"name": "conversation_retention_days", "value": 90}, {"name": "enable_usage_analytics", "value": true}, {"name": "enable_cost_tracking", "value": true}, {"name": "max_tokens_per_request", "value": 4000}, {"name": "max_conversation_length", "value": 50}, {"name": "enable_content_moderation", "value": false}, {"name": "academic_curriculum_focus", "value": "nigerian_cambridge"}, {"name": "academic_default_level", "value": "sss"}, {"name": "assistant_context_modules", "value": []}, {"name": "enable_global_openai", "value": false}, {"name": "global_openai_api_key", "value": "", "is_secret": true}, {"name": "enable_global_gemini", "value": false}, {"name": "global_gemini_api_key", "value": "", "is_secret": true}, {"name": "enable_global_deepseek", "value": false}, {"name": "global_deepseek_api_key", "value": "", "is_secret": true}, {"name": "enable_global_anthropic", "value": false}, {"name": "global_anthropic_api_key", "value": "", "is_secret": true}, {"name": "enable_global_xai", "value": false}, {"name": "global_xai_api_key", "value": "", "is_secret": true}, {"name": "fallback_enabled", "value": true}, {"name": "save_conversations", "value": true}], "module": []}