<?php

namespace App\Http\Controllers\AI;

use App\Http\Controllers\Controller;
use App\Services\AI\AcademicChatService;
use Illuminate\Http\Request;

class AcademicChatController extends Controller
{
    public function askQuestion(Request $request, AcademicChatService $service)
    {
        $request->validate([
            'question' => 'required|string|max:500000',
            'subject' => 'nullable|string|max:1000',
            'level' => 'nullable|string|max:500',
            'category' => 'nullable|string|max:1000',
        ]);

        $result = $service->askQuestion($request);

        return response()->success([
            'message' => trans('ai.academic.question_answered'),
            'result' => $result,
        ]);
    }

    public function generateLessonPlan(Request $request, AcademicChatService $service)
    {
        // Check if user has lesson plan generation permission
        if (!auth()->user()->can('ai:generate-lesson-plan')) {
            return response()->json(['message' => trans('user.errors.permission_denied')], 403);
        }

        $request->validate([
            'topic' => 'required|string|max:255',
            'subject' => 'required|string|max:100',
            'level' => 'required|string|max:50',
            'duration' => 'nullable|integer|min:10|max:300',
            'objectives' => 'nullable|array',
            'objectives.*' => 'string|max:25500',
        ]);

        $result = $service->generateLessonPlan($request);

        return response()->success([
            'message' => trans('ai.academic.lesson_plan_generated'),
            'result' => $result,
        ]);
    }

    public function generateQuiz(Request $request, AcademicChatService $service)
    {
        // Check if user has quiz generation permission
        if (!auth()->user()->can('ai:generate-quiz')) {
            return response()->json(['message' => trans('user.errors.permission_denied')], 403);
        }

        $request->validate([
            'topic' => 'required|string|max:255',
            'subject' => 'required|string|max:100',
            'level' => 'required|string|max:50',
            'question_count' => 'nullable|integer|min:1|max:50',
            'question_types' => 'nullable|array',
            'question_types.*' => 'string|in:multiple_choice,short_answer,essay,true_false',
        ]);

        $result = $service->generateQuiz($request);

        return response()->success([
            'message' => trans('ai.academic.quiz_generated'),
            'result' => $result,
        ]);
    }
}
