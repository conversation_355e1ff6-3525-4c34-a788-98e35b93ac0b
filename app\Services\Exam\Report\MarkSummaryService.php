<?php

namespace App\Services\Exam\Report;

use App\Actions\Student\FetchBatchWiseStudent;
use App\Enums\Exam\AssessmentAttempt;
use App\Http\Resources\Exam\ExamResource;
use App\Models\Academic\Batch;
use App\Models\Academic\Subject;
use App\Models\Academic\SubjectRecord;
use App\Models\Exam\Exam;
use App\Models\Exam\Grade;
use App\Models\Exam\Schedule;
use App\Models\Incharge;
use App\Models\Student\Student;
use App\Models\Student\SubjectWiseStudent;
use App\Support\HasGrade;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Enum;

class MarkSummaryService
{
    use HasGrade;

    public function preRequisite(): array
    {
        $exams = ExamResource::collection(Exam::query()
            ->with('term.division')
            ->byPeriod()
            ->get());

        $attempts = AssessmentAttempt::getOptions();

        return compact('exams', 'attempts');
    }

    public function fetchReport(Request $request)
    {
        $request->validate([
            'exam' => 'required|uuid',
            'attempt' => ['required', new Enum(AssessmentAttempt::class)],
            'batch' => 'required|uuid',
            'title' => 'string|nullable|max:255',
            'signatory_1' => 'string|nullable|max:255',
            'signatory_2' => 'string|nullable|max:255',
            'signatory_3' => 'string|nullable|max:255',
            'signatory_4' => 'string|nullable|max:255',
        ]);

        $exam = Exam::query()
            ->with('term.division')
            ->byPeriod()
            ->whereUuid($request->exam)
            ->getOrFail(trans('exam.exam'), 'exam');

        $batch = Batch::query()
            ->byPeriod()
            ->filterAccessible()
            ->whereUuid($request->batch)
            ->getOrFail(trans('academic.batch.batch'), 'batch');

        $schedule = Schedule::query()
            ->with(['grade', 'assessment', 'records.subject'])
            ->whereExamId($exam->id)
            ->whereBatchId($batch->id)
            ->where('attempt', $request->attempt)
            ->getOrFail(trans('exam.schedule.schedule'));

        $subjectRecords = SubjectRecord::query()
            ->where(function ($q) use ($batch) {
                $q->where('course_id', $batch->course_id)
                    ->orWhere('batch_id', $batch->id);
            })
            ->whereIn('subject_id', $schedule->records->pluck('subject_id'))
            ->get();

        $subjectWiseStudents = SubjectWiseStudent::query()
            ->whereBatchId($batch->id)
            ->get();

        // Get report sheet display configuration
        $showInReportSheet = config('config.exam.show_in_report_sheet', []);

        $params = [
            'subject_wise_students' => $subjectWiseStudents,
            'show_class_average_mark' => in_array('show_class_average_mark', $showInReportSheet),
            'show_class_highest_mark' => in_array('show_class_highest_mark', $showInReportSheet),
            'show_class_lowest_mark' => in_array('show_class_lowest_mark', $showInReportSheet),
            'show_subject_position' => in_array('show_subject_position', $showInReportSheet),
            'show_course_position' => in_array('show_course_position', $showInReportSheet),
        ];

        $inputSubjects = Subject::query()
            ->byPeriod()
            ->whereIn('uuid', Str::toArray($request->query('subjects')))
            ->get();

        $request->merge([
            'select_all' => true,
        ]);

        $students = (new FetchBatchWiseStudent)->execute($request->all());

        $scheduleAssessment = collect($schedule->assessment->records ?? []);
        $grade = $schedule->grade;

        $failGrades = collect($grade->records)->filter(function ($record) {
            return Arr::get($record, 'is_fail_grade');
        })->pluck('code')->all();

        $params['fail_grades'] = $failGrades;

        $rows = [];
        $header = [];
        $subHeader = [];
        $marks = [];
        array_push($header, ['key' => 'sno', 'rowspan' => 2, 'class' => 'font-weight-bold', 'label' => trans('general.sno')]);
        array_push($header, ['key' => 'roll_number', 'rowspan' => 2, 'class' => 'font-weight-bold', 'label' => trans('student.roll_number.roll_number')]);
        array_push($header, ['key' => 'name', 'rowspan' => 2, 'class' => 'font-weight-bold', 'label' => trans('student.props.name')]);

        $subjects = [];
        $maxTotal = 0;
        foreach ($schedule->records as $examRecord) {
            if ($inputSubjects->count() && ! in_array($examRecord->subject_id, $inputSubjects->pluck('id')->toArray())) {
                continue;
            }

            $hasExam = $examRecord->getConfig('has_exam');

            if (! $hasExam) {
                continue;
            }

            $subjectRecord = $subjectRecords->firstWhere('subject_id', $examRecord->subject_id);

            $recordMarks = $examRecord->marks ?? [];
            $notApplicableStudents = $examRecord->getConfig('not_applicable_students', []);

            $assessments = collect($examRecord->getConfig('assessments', []))
                ->filter(function ($assessment) {
                    return Arr::get($assessment, 'max_mark', 0) > 0;
                })
                ->map(function ($assessment) use ($scheduleAssessment) {
                    $code = Arr::get($assessment, 'code');

                    return [
                        'name' => Arr::get($scheduleAssessment->firstWhere('code', $code), 'name'),
                        'code' => $code,
                        'max_mark' => Arr::get($assessment, 'max_mark', 0),
                    ];
                });

            $subjects[] = [
                'id' => $examRecord->subject_id,
                'name' => $examRecord->subject->name,
                'shortcode' => $examRecord->subject->shortcode,
                'assessments' => $assessments,
                'position' => $examRecord->subject->position,
                'total' => $assessments->sum('max_mark'),
                'marks' => $recordMarks,
                'not_applicable_students' => $notApplicableStudents,
                'is_elective' => (bool) $subjectRecord?->is_elective,
                'has_grading' => (bool) $subjectRecord?->has_grading,
            ];
        }

        $subjects = collect($subjects)->sortBy('position')->toArray();

        $mainSubjects = collect($subjects)->filter(function ($subject) {
            return ! $subject['has_grading'];
        })->toArray();

        $gradingSubjects = collect($subjects)->filter(function ($subject) {
            return $subject['has_grading'];
        })->toArray();

        $gradingSubjectAssessmentCount = collect($gradingSubjects)->sum(function ($subject) {
            return count($subject['assessments']);
        });

        $maxTotal = collect($mainSubjects)->sum('total');

        [$header, $subHeader, $marks] = $this->updateSubjects($header, $subHeader, $marks, $mainSubjects, $params);

        array_push($header, ['key' => 'total', 'class' => 'text-center font-weight-bold', 'rowspan' => 2, 'label' => trans('exam.total')]);
        array_push($header, ['key' => 'percent', 'class' => 'text-center font-weight-bold', 'rowspan' => 2, 'label' => trans('exam.percent')]);
        array_push($header, ['key' => 'grade', 'class' => 'text-center font-weight-bold', 'rowspan' => 2, 'label' => trans('exam.result_grade')]);

        [$header, $subHeader, $marks] = $this->updateSubjects($header, $subHeader, $marks, $gradingSubjects, $params);

        $marks = collect($marks);
        $i = 0;
        foreach ($students as $index => $student) {
            $studentTotal = 0;
            $row = [];

            $row = $this->getStudentMarks($student, $grade, $marks, $mainSubjects, $row, $params);

            if (collect($row)->filter(function ($row) {
                return Arr::get($row, 'type') == 'marks' && Arr::get($row, 'label') == '';
            })->count() == count($row)) {
                continue;
            }

            $i++;

            array_unshift($row, ['key' => 'name', 'label' => $student->name.' ('.$student->code_number.')']);
            array_unshift($row, ['key' => 'roll_number', 'label' => $student->roll_number]);
            array_unshift($row, ['key' => 'sno', 'label' => $i]);

            $hasFailGrade = collect($row)->filter(function ($row) use ($failGrades) {
                return in_array(Arr::get($row, 'grade'), $failGrades);
            })->count();

            // Only include assessment marks (exclude subject_total and subject_position types) in the total calculation
            $studentMaxMarkTotal = collect($row)->filter(function ($item) {
                $type = Arr::get($item, 'type');
                return $type !== 'subject_total' && $type !== 'subject_position';
            })->sum('max_mark');

            $studentTotal = collect($row)->filter(function ($item) {
                $type = Arr::get($item, 'type');
                return $type !== 'subject_total' && $type !== 'subject_position';
            })->sum('numeric_mark');

            $row[] = ['key' => 'total', 'class' => 'text-center', 'label' => $studentTotal];

            $studentPercent = $studentMaxMarkTotal > 0 ? round(($studentTotal / $studentMaxMarkTotal) * 100, 2) : 0;

            $row[] = ['key' => 'percent', 'class' => 'text-center', 'label' => $studentPercent];

            $studentGrade = $this->getGrade($grade, $studentMaxMarkTotal, $studentTotal);

            $row[] = ['key' => 'grade', 'class' => 'text-center', 'label' => $studentGrade, 'text-style' => $hasFailGrade ? 'circular-border' : ''];

            $row = $this->getStudentMarks($student, $grade, $marks, $gradingSubjects, $row);

            $rows[] = $row;
        }

        $highestRow[] = ['key' => 'highest', 'class' => 'font-weight-bold', 'label' => trans('exam.highest_mark'), 'colspan' => 3];
        $lowestRow[] = ['key' => 'lowest', 'class' => 'font-weight-bold', 'label' => trans('exam.lowest_mark'), 'colspan' => 3];
        $averageRow[] = ['key' => 'average', 'class' => 'font-weight-bold', 'label' => trans('exam.average_mark'), 'colspan' => 3];
        $absentRow[] = ['key' => 'average', 'class' => 'font-weight-bold', 'label' => trans('exam.total_absent'), 'colspan' => 3];
        $passRow[] = ['key' => 'pass', 'class' => 'font-weight-bold', 'label' => trans('global.total', ['attribute' => trans('exam.results.pass')]), 'colspan' => 3];
        $failRow[] = ['key' => 'fail', 'class' => 'font-weight-bold', 'label' => trans('global.total', ['attribute' => trans('exam.results.fail')]), 'colspan' => 3];

        foreach ($mainSubjects as $subject) {
            // Process each assessment column
            foreach ($subject['assessments'] as $assessment) {
                $marks = [];
                foreach ($rows as $row) {
                    $marks[] = collect($row)->filter(function ($row) use ($subject, $assessment) {
                        return Arr::get($row, 'subject_id') == $subject['id'] && Arr::get($row, 'assessment_code') == $assessment['code'];
                    })->first();
                }

                $highestRow[] = ['key' => "highest{$subject['id']}_{$assessment['code']}", 'class' => 'text-center font-weight-bold', 'label' => collect($marks)->max('numeric_mark')];

                $lowestRow[] = ['key' => "lowest{$subject['id']}_{$assessment['code']}", 'class' => 'text-center font-weight-bold', 'label' => collect($marks)->filter(function ($mark) {
                    return is_numeric(Arr::get($mark, 'label'));
                })->min('numeric_mark')];

                $averageRow[] = ['key' => "average{$subject['id']}_{$assessment['code']}", 'class' => 'text-center font-weight-bold', 'label' => round(collect($marks)
                    ->filter(function ($mark) {
                        return Arr::get($mark, 'label') != 'NA';
                    })->average('numeric_mark'), 2)];

                $absentRow[] = ['key' => "absent{$subject['id']}_{$assessment['code']}", 'class' => 'text-center font-weight-bold', 'label' => collect($marks)->whereIn('label', ['A', 'a', 'Ab', 'ab'])->count()];

                $failRow[] = ['key' => "fail{$subject['id']}_{$assessment['code']}", 'class' => 'text-center font-weight-bold', 'label' => collect($marks)->filter(function ($mark) use ($failGrades) {
                    return in_array(Arr::get($mark, 'grade'), $failGrades);
                })->count()];

                $passRow[] = ['key' => "pass{$subject['id']}_{$assessment['code']}", 'class' => 'text-center font-weight-bold', 'label' => collect($marks)->filter(function ($mark) use ($failGrades) {
                    return ! in_array(Arr::get($mark, 'label'), ['NA', 'A', 0 , '0']) && ! in_array(Arr::get($mark, 'grade'), $failGrades);
                })->count()];
            }

            // Process subject total column only for average row
            $totalMarks = [];
            foreach ($rows as $row) {
                $totalMarks[] = collect($row)->filter(function ($row) use ($subject) {
                    return Arr::get($row, 'subject_id') == $subject['id'] && Arr::get($row, 'key') == "subject{$subject['id']}_total";
                })->first();
            }

            // Filter valid total marks for calculations
            $validTotalMarks = collect($totalMarks)->filter(function ($mark) {
                return Arr::get($mark, 'label') != 'NA' && is_numeric(Arr::get($mark, 'numeric_mark'));
            });

            // Calculate highest and lowest subject totals
            $highestSubjectTotal = $validTotalMarks->count() > 0 ? $validTotalMarks->max('numeric_mark') : '';
            $lowestSubjectTotal = $validTotalMarks->count() > 0 ? $validTotalMarks->min('numeric_mark') : '';

            // Calculate the average directly from student subject totals
            // This ensures we're using the actual average of totals, not the sum of assessment averages
            $subjectTotalAverage = 0;
            if ($validTotalMarks->count() > 0) {
                $subjectTotalAverage = round($validTotalMarks->average('numeric_mark'), 2);
            }

            // Add highest and lowest subject total marks
            $highestRow[] = [
                'key' => "highest{$subject['id']}_total",
                'class' => 'text-center font-weight-bold',
                'label' => $highestSubjectTotal
            ];

            $lowestRow[] = [
                'key' => "lowest{$subject['id']}_total",
                'class' => 'text-center font-weight-bold',
                'label' => $lowestSubjectTotal
            ];

            // Add empty cells for position columns in summary rows only if enabled
            if (Arr::get($params, 'show_subject_position', false)) {
                $highestRow[] = [
                    'key' => "highest{$subject['id']}_position",
                    'class' => 'text-center',
                    'label' => ''
                ];

                $lowestRow[] = [
                    'key' => "lowest{$subject['id']}_position",
                    'class' => 'text-center',
                    'label' => ''
                ];
            }

            // Only keep the average for subject total
            $averageRow[] = [
                'key' => "average{$subject['id']}_total",
                'class' => 'text-center font-weight-bold',
                'label' => $subjectTotalAverage
            ];

            // Add empty cell for position column in average row only if enabled
            if (Arr::get($params, 'show_subject_position', false)) {
                $averageRow[] = [
                    'key' => "average{$subject['id']}_position",
                    'class' => 'text-center',
                    'label' => ''
                ];
            }

            // Calculate total absent, fail, and pass for subject totals
            $totalAbsent = collect($totalMarks)->whereIn('label', ['NA'])->count();

            // Count fails based on grade in the subject total
            $totalFail = collect($totalMarks)->filter(function ($mark) use ($failGrades) {
                $label = Arr::get($mark, 'label');
                // Only count as fail if it's not NA and has a fail grade
                return $label !== 'NA' && in_array(Arr::get($mark, 'grade'), $failGrades);
            })->count();

            // Count passes (not NA, not empty, and not fail grade)
            $totalPass = collect($totalMarks)->filter(function ($mark) use ($failGrades) {
                $label = Arr::get($mark, 'label');
                $numericMark = Arr::get($mark, 'numeric_mark');
                // Only count as pass if it has a valid mark and is not a fail grade
                return $label !== 'NA' && !empty($numericMark) && $numericMark > 0 && !in_array(Arr::get($mark, 'grade'), $failGrades);
            })->count();

            // Add cells for absent, fail, and pass rows
            $absentRow[] = [
                'key' => "absent{$subject['id']}_total",
                'class' => 'text-center font-weight-bold',
                'label' => $totalAbsent
            ];

            $failRow[] = [
                'key' => "fail{$subject['id']}_total",
                'class' => 'text-center font-weight-bold',
                'label' => $totalFail
            ];

            $passRow[] = [
                'key' => "pass{$subject['id']}_total",
                'class' => 'text-center font-weight-bold',
                'label' => $totalPass
            ];

            // Add empty cells for position columns in summary rows only if enabled
            if (Arr::get($params, 'show_subject_position', false)) {
                $absentRow[] = [
                    'key' => "absent{$subject['id']}_position",
                    'class' => 'text-center',
                    'label' => ''
                ];

                $failRow[] = [
                    'key' => "fail{$subject['id']}_position",
                    'class' => 'text-center',
                    'label' => ''
                ];

                $passRow[] = [
                    'key' => "pass{$subject['id']}_position",
                    'class' => 'text-center',
                    'label' => ''
                ];
            }
        }

        // Calculate the correct colspan for the extra columns
        // Each subject now has an extra position column if enabled, so we need to account for that
        $extraColspan = 3 + $gradingSubjectAssessmentCount;
        if (Arr::get($params, 'show_subject_position', false)) {
            $extraColspan += count($mainSubjects); // +1 for each subject's position column
        }

        $highestRow[] = ['key' => 'extra', 'label' => '', 'colspan' => $extraColspan];
        $lowestRow[] = ['key' => 'extra', 'label' => '', 'colspan' => $extraColspan];
        $averageRow[] = ['key' => 'extra', 'label' => '', 'colspan' => $extraColspan];
        $absentRow[] = ['key' => 'extra', 'label' => '', 'colspan' => $extraColspan];
        $failRow[] = ['key' => 'extra', 'label' => '', 'colspan' => $extraColspan];
        $passRow[] = ['key' => 'extra', 'label' => '', 'colspan' => $extraColspan];

        // Calculate subject positions for each student only if enabled
        if (Arr::get($params, 'show_subject_position', false)) {
            foreach ($mainSubjects as $subject) {
                $subjectId = $subject['id'];

            // Collect all student marks for this subject
            $subjectMarks = [];
            foreach ($rows as $studentIndex => $row) {
                $totalMark = collect($row)->filter(function ($item) use ($subjectId) {
                    return $item['key'] === "subject{$subjectId}_total";
                })->first();

                // Only include students with valid marks (not NA) in the position calculation
                if ($totalMark && is_numeric($totalMark['numeric_mark']) && $totalMark['label'] !== 'NA' && $totalMark['numeric_mark'] > 0) {
                    $subjectMarks[] = [
                        'student_index' => $studentIndex,
                        'mark' => $totalMark['numeric_mark'],
                    ];
                }
            }

            // Sort by marks in descending order
            usort($subjectMarks, function($a, $b) {
                return $b['mark'] <=> $a['mark'];
            });

            // Assign positions (1, 2, 3, etc.)
            $positions = [];
            $currentPosition = 1;
            $previousMark = null;
            $sameRankCount = 0;

            // First pass: determine positions with proper handling of ties
            foreach ($subjectMarks as $index => $markData) {
                $mark = $markData['mark'];

                if ($previousMark !== null && $mark === $previousMark) {
                    // Same mark as previous student, assign same position
                    $positions[] = $currentPosition;
                    $sameRankCount++;
                } else {
                    // Different mark, assign next position accounting for any ties
                    $currentPosition = $index + 1;
                    $positions[] = $currentPosition;
                    $previousMark = $mark;
                    $sameRankCount = 0;
                }
            }

            // Second pass: update the position in the rows
            foreach ($subjectMarks as $index => $markData) {
                $studentIndex = $markData['student_index'];
                $position = $positions[$index];

                // Find the position column for this subject and update it
                foreach ($rows[$studentIndex] as $key => $item) {
                    if ($item['key'] === "subject{$subjectId}_position") {
                        $rows[$studentIndex][$key]['label'] = $position;
                        break;
                    }
                }
            }
        }
        }

        // Sort student rows by percentage in descending order (best to least)
        usort($rows, function($a, $b) {
            $percentA = collect($a)->firstWhere('key', 'percent')['label'] ?? 0;
            $percentB = collect($b)->firstWhere('key', 'percent')['label'] ?? 0;
            return $percentB <=> $percentA; // Sort in descending order
        });

        // Update serial numbers to reflect new order
        foreach ($rows as $index => $row) {
            foreach ($row as $key => $item) {
                if ($item['key'] === 'sno') {
                    $rows[$index][$key]['label'] = $index + 1;
                    break;
                }
            }
        }

        // Add summary rows based on configuration
        if (Arr::get($params, 'show_class_highest_mark')) {
            $rows[] = $highestRow;
        }
        if (Arr::get($params, 'show_class_lowest_mark')) {
            $rows[] = $lowestRow;
        }
        if (Arr::get($params, 'show_class_average_mark')) {
            $rows[] = $averageRow;
        }
        $rows[] = $absentRow;
        $rows[] = $passRow;
        $rows[] = $failRow;

        $signatoryRow[] = ['key' => 'signatory', 'class' => 'font-weight-bold', 'label' => trans('academic.teacher'), 'colspan' => 3, 'height' => 40];
        foreach ($mainSubjects as $subject) {
            foreach ($subject['assessments'] as $assessment) {
                $signatoryRow[] = ['key' => 'sign', 'label' => ''];
            }
            // Add empty cells for subject total and conditionally position columns
            $signatoryRow[] = ['key' => 'sign_total', 'label' => ''];
            if (Arr::get($params, 'show_subject_position', false)) {
                $signatoryRow[] = ['key' => 'sign_position', 'label' => ''];
            }
        }

        $signatoryRow[] = ['key' => 'extra', 'label' => '', 'colspan' => $extraColspan];

        $rows[] = $signatoryRow;

        // Add course position row if enabled
        if (Arr::get($params, 'show_course_position', false)) {
            $coursePositionRow = [];
            $coursePositionRow[] = ['key' => 'course_position_label', 'class' => 'font-weight-bold', 'label' => trans('exam.course_position'), 'colspan' => 3];

            foreach ($mainSubjects as $subject) {
                foreach ($subject['assessments'] as $assessment) {
                    $coursePositionRow[] = ['key' => 'course_pos', 'label' => ''];
                }
                // Add empty cells for subject total and conditionally position columns
                $coursePositionRow[] = ['key' => 'course_pos_total', 'label' => ''];
                if (Arr::get($params, 'show_subject_position', false)) {
                    $coursePositionRow[] = ['key' => 'course_pos_position', 'label' => ''];
                }
            }

            $coursePositionRow[] = ['key' => 'course_position_value', 'label' => '', 'colspan' => $extraColspan];
            $rows[] = $coursePositionRow;
        }

        $incharges = Incharge::query()
            ->whereHasMorph(
                'model',
                [Batch::class],
                function (Builder $query) use ($batch) {
                    $query->where('id', $batch->id);
                }
            )
            ->with(['employee' => fn ($q) => $q->summary()])
            ->get();

        $batchIncharges = implode(', ', $incharges->map(function ($incharge) {
            return $incharge->employee->name;
        })->toArray());

        $titles = [
            [
                'label' => $request->query('title', trans('exam.report.mark_summary.mark_summary')),
                'align' => 'center',
                'class' => 'heading',
            ],
            [
                'label' => $exam->name,
                'align' => 'center',
                'class' => 'mt-2 sub-heading',
            ],
            [
                'label' => $batch->course->name.' '.$batch->name,
                'align' => 'center',
                'class' => 'mt-2 sub-heading',
            ],
            [
                'label' => trans('academic.class_teacher').' : '.($batchIncharges ?: '-'),
                'align' => 'center',
                'class' => 'mt-2 sub-heading',
            ],
        ];

        $layout = [
            'show_print_date_time' => $request->boolean('show_print_date_time'),
            'signatory1' => $request->query('signatory_1'),
            'signatory2' => $request->query('signatory_2'),
            'signatory3' => $request->query('signatory_3'),
            'signatory4' => $request->query('signatory_4'),
            'watermark' => $request->query('show_watermark', false),
        ];

        // If action is pdf, generate PDF
        if ($request->action === 'pdf') {
            $content = view('print.exam.report.mark-summary', compact('titles', 'rows', 'header', 'subHeader', 'layout', 'request'))->render();

            $mpdf = new \Mpdf\Mpdf([
                'mode' => 'utf-8',
                'format' => $request->paper_size ?? 'A4-L', // Default to landscape A4
                'margin_left' => 10,
                'margin_right' => 10,
                'margin_top' => 10,
                'margin_bottom' => 10,
            ]);
            $mpdf->autoScriptToLang = true;
            $mpdf->autoLangToFont = true;
            $mpdf->WriteHTML($content);

            // Set filename based on exam and batch
            $filename = Str::slug($exam->name . '-' . $batch->name . '-mark-summary') . '.pdf';

            // Output the PDF for download
            return $mpdf->Output($filename, 'D');
        }

        // Add PDF download URL to the view
        $pdfUrl = route('exam.reports.mark-summary.downloadPdf', $request->all());

        $content = view('print.exam.report.mark-summary', compact('titles', 'rows', 'header', 'subHeader', 'layout', 'pdfUrl', 'request'))->render();

        // Add PDF download URL as meta tag
        $content = str_replace('</head>', '<meta name="pdf-download-url" content="'.$pdfUrl.'"></head>', $content);

        return $content;
    }

    private function updateSubjects(array $header, array $subHeader, array $marks, array $subjects, array $params = [])
    {
        $showSubjectPosition = Arr::get($params, 'show_subject_position', false);

        foreach ($subjects as $subject) {
            $subjectId = Arr::get($subject, 'id');
            $assessments = Arr::get($subject, 'assessments', []);
            $subjectMarks = Arr::get($subject, 'marks', []);
            $subjectTotal = Arr::get($subject, 'total', 0);

            // Update the colspan to include the total and conditionally position columns
            $colspan = count($assessments) + 1; // +1 for total
            if ($showSubjectPosition) {
                $colspan += 1; // +1 for position if enabled
            }

            array_push($header, [
                'key' => "subject{$subjectId}",
                'class' => 'text-center font-weight-bold',
                'colspan' => $colspan,
                'label' => Arr::get($subject, 'shortcode'),
            ]);

            foreach ($assessments as $assessment) {
                array_push($subHeader, [
                    'key' => "subject{$subjectId}_{$assessment['code']}",
                    'class' => 'text-center font-weight-bold',
                    'label' => $assessment['code'].' ('.$assessment['max_mark'].')',
                    'text' => $assessment['name'],
                ]);

                $assessmentMarks = collect($subjectMarks)->firstWhere('code', $assessment['code']);

                $marks[] = [
                    'subject_id' => $subjectId,
                    'assessment_code' => $assessment['code'],
                    'max_mark' => $assessment['max_mark'],
                    'marks' => $assessmentMarks['marks'] ?? [],
                ];
            }

            // Add the total column for this subject
            array_push($subHeader, [
                'key' => "subject{$subjectId}_total",
                'class' => 'text-center font-weight-bold',
                'label' => trans('exam.total').' ('.$subjectTotal.')',
                'text' => trans('exam.total'),
            ]);

            // Add the position/rank column for this subject only if enabled
            if ($showSubjectPosition) {
                array_push($subHeader, [
                    'key' => "subject{$subjectId}_position",
                    'class' => 'text-center font-weight-bold',
                    'label' => trans('exam.position'),
                    'text' => trans('exam.position'),
                ]);
            }
        }

        return [$header, $subHeader, $marks];
    }

    private function getStudentMarks(Student $student, Grade $grade, Collection $marks, array $subjects, array $row, array $params = [])
    {
        $subjectWiseStudents = Arr::get($params, 'subject_wise_students', collect([]));

        foreach ($subjects as $subject) {
            $notApplicableStudents = $subject['not_applicable_students'] ?? [];
            $subjectId = $subject['id'];
            $subjectTotal = Arr::get($subject, 'total', 0);

            $isElective = false;

            if (Arr::get($subject, 'is_elective')) {
                $isElective = $subjectWiseStudents
                    ->where('student_id', $student->id)
                    ->where('subject_id', $subject['id'])
                    ->first() ? true : false;
            }

            if (in_array($student->uuid, $notApplicableStudents)) {
                foreach ($subject['assessments'] as $assessment) {
                    $row[] = [
                        'subject_id' => $subject['id'],
                        'assessment_code' => $assessment['code'],
                        'key' => "subject{$subject['id']}_{$assessment['code']}",
                        'label' => 'NA',
                        'class' => 'text-center',
                        'max_mark' => 0,
                        'numeric_mark' => 0,
                        'grade' => '',
                    ];
                }

                // Add NA for subject total as well with consistent styling
                $row[] = [
                    'subject_id' => $subject['id'],
                    'key' => "subject{$subject['id']}_total",
                    'type' => 'subject_total',
                    'label' => 'NA',
                    'class' => 'text-center',
                    'max_mark' => 0,
                    'numeric_mark' => 0,
                    'grade' => '',
                ];

                // Add NA for subject position as well only if enabled
                if (Arr::get($params, 'show_subject_position', false)) {
                    $row[] = [
                        'subject_id' => $subject['id'],
                        'key' => "subject{$subject['id']}_position",
                        'type' => 'subject_position',
                        'label' => 'NA',
                        'class' => 'text-center',
                        'numeric_mark' => 0,
                    ];
                }

                continue;
            }

            // Track subject total for this student
            $studentSubjectTotal = 0;
            $studentSubjectMaxTotal = 0;
            $subjectMarks = [];

            foreach ($subject['assessments'] as $assessment) {
                $assessmentMark = $marks
                    ->where('subject_id', $subject['id'])
                    ->where('assessment_code', $assessment['code'])
                    ->first();

                $mark = collect($assessmentMark['marks'] ?? []);

                $maxMark = $assessment['max_mark'] ?? 0;

                $studentMark = $mark->firstWhere('uuid', $student->uuid);
                $obtainedMark = $studentMark['obtained_mark'] ?? '';

                $label = $studentMark['obtained_mark'] ?? '';

                if ($subject['has_grading']) {
                    $label = $this->getGrade($grade, $assessment['max_mark'], $obtainedMark);
                }

                $markClass = 'text-center';
                $subjectGrade = $this->getGrade($grade, $assessment['max_mark'], $obtainedMark);

                $failGrades = Arr::get($params, 'fail_grades', []);
                if (in_array($subjectGrade, $failGrades)) {
                    $markClass = 'text-center font-weight-bold';
                }

                if ($isElective) {
                    $label .= '*';
                }

                // Handle empty, null, or non-numeric values as 0, and display as 'NA' for consistency
                if ($label === 'NA') {
                    $numericMark = 0;
                    $label = 'NA';
                } else if (empty($obtainedMark) || $obtainedMark === '' || $obtainedMark === null || !is_numeric($obtainedMark)) {
                    $numericMark = 0;
                    $label = 0; // Change empty values to 'NA' for consistent display
                } else {
                    $numericMark = (float)$obtainedMark;
                }

                // Add to subject total
                $studentSubjectTotal += $numericMark;
                $studentSubjectMaxTotal += $maxMark;

                $markItem = [
                    'subject_id' => $subject['id'],
                    'assessment_code' => $assessment['code'],
                    'key' => "subject{$subject['id']}_{$assessment['code']}",
                    'type' => 'marks',
                    'label' => $label,
                    'class' => $markClass,
                    'text-style' => in_array($subjectGrade, $failGrades) ? 'circular-border' : '',
                    'max_mark' => $maxMark,
                    'numeric_mark' => $numericMark,
                    'grade' => $subjectGrade,
                ];

                $row[] = $markItem;
                $subjectMarks[] = $markItem;
            }

            // Calculate subject grade based on total
            $subjectGrade = $this->getGrade($grade, $studentSubjectMaxTotal, $studentSubjectTotal);

            // Add subject total column
            $markClass = 'text-center';
            $failGrades = Arr::get($params, 'fail_grades', []);

            if (in_array($subjectGrade, $failGrades)) {
                $markClass .= ' text-danger';
            }

            if ($isElective) {
                $studentSubjectTotal .= '*';
            }

            // Add subject total column with consistent styling
            $totalClass = $markClass;
            if ($studentSubjectTotal === 0 || $studentSubjectTotal === '0') {
                $studentSubjectTotal = '0';
            }

            $row[] = [
                'subject_id' => $subject['id'],
                'key' => "subject{$subject['id']}_total",
                'type' => 'subject_total',
                'label' => $studentSubjectTotal,
                'class' => $totalClass,
                'text-style' => in_array($subjectGrade, $failGrades) ? 'circular-border' : '',
                'max_mark' => $studentSubjectMaxTotal,
                'numeric_mark' => is_numeric($studentSubjectTotal) ? $studentSubjectTotal : 0,
                'grade' => $subjectGrade,
            ];

            // Add a placeholder for the position column - will be calculated later only if enabled
            if (Arr::get($params, 'show_subject_position', false)) {
                $positionClass = 'text-center';

                $row[] = [
                    'subject_id' => $subject['id'],
                    'key' => "subject{$subject['id']}_position",
                    'type' => 'subject_position',
                    'label' => '', // Will be filled in later
                    'class' => $positionClass,
                    'numeric_mark' => is_numeric($studentSubjectTotal) ? $studentSubjectTotal : 0,
                ];
            }
        }

        return $row;
    }
}
