<template>
    <PageHeader
        :title="$trans(route.meta.label)"
        :navs="[{ label: $trans('exam.exam'), path: 'Exam' }]"
    >
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <FormAction
            :pre-requisites="false"
            @setPreRequisites="setPreRequisites"
            :init-url="initUrl"
            data-fetch="exam"
            :init-form="initForm"
            :form="form"
            action="store"
            stay-on
            redirect="Exam"
        >
            <div class="grid grid-cols-3 gap-4">
                <div class="col-span-3 sm:col-span-1">
                    <BaseInput
                        type="text"
                        v-model="form.marksheetFormat"
                        name="marksheetFormat"
                        :label="$trans('exam.config.props.marksheet_format')"
                        v-model:error="formErrors.marksheetFormat"
                    />
                </div>
                <div class="col-span-3">
                    <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
                        {{ $trans('exam.config.props.visible_marksheet_types') }}
                    </h4>
                    <div class="flex flex-wrap gap-4">
                        <BaseCheckbox
                            v-model="checkboxStates.cumulative"
                            name="visibleMarksheetTypes_cumulative"
                            :label="$trans('exam.marksheet.cumulative')"
                            @update:modelValue="updateVisibleMarksheetTypes('cumulative', $event)"
                        />
                        <BaseCheckbox
                            v-model="checkboxStates.term_wise"
                            name="visibleMarksheetTypes_term_wise"
                            :label="$trans('exam.marksheet.term_wise')"
                            @update:modelValue="updateVisibleMarksheetTypes('term_wise', $event)"
                        />
                        <BaseCheckbox
                            v-model="checkboxStates.exam_wise_credit_based"
                            name="visibleMarksheetTypes_exam_wise_credit_based"
                            :label="$trans('exam.marksheet.exam_wise_credit_based')"
                            @update:modelValue="updateVisibleMarksheetTypes('exam_wise_credit_based', $event)"
                        />
                        <BaseCheckbox
                            v-model="checkboxStates.exam_wise"
                            name="visibleMarksheetTypes_exam_wise"
                            :label="$trans('exam.marksheet.exam_wise')"
                            @update:modelValue="updateVisibleMarksheetTypes('exam_wise', $event)"
                        />
                        <BaseCheckbox
                            v-model="checkboxStates.exam_wise_default"
                            name="visibleMarksheetTypes_exam_wise_default"
                            :label="$trans('exam.marksheet.exam_wise_default')"
                            @update:modelValue="updateVisibleMarksheetTypes('exam_wise_default', $event)"
                        />
                    </div>
                </div>
                <div class="col-span-3">
                    <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
                        {{ $trans('exam.config.props.show_in_report_sheet') }}
                    </h4>
                    <div class="flex flex-wrap gap-4">
                        <BaseCheckbox
                            v-model="showInReportSheetStates.show_class_average_mark"
                            name="showInReportSheet_show_class_average_mark"
                            :label="$trans('exam.config.props.show_class_average_mark')"
                            @update:modelValue="updateShowInReportSheet('show_class_average_mark', $event)"
                        />
                        <BaseCheckbox
                            v-model="showInReportSheetStates.show_class_highest_mark"
                            name="showInReportSheet_show_class_highest_mark"
                            :label="$trans('exam.config.props.show_class_highest_mark')"
                            @update:modelValue="updateShowInReportSheet('show_class_highest_mark', $event)"
                        />
                        <BaseCheckbox
                            v-model="showInReportSheetStates.show_class_lowest_mark"
                            name="showInReportSheet_show_class_lowest_mark"
                            :label="$trans('exam.config.props.show_class_lowest_mark')"
                            @update:modelValue="updateShowInReportSheet('show_class_lowest_mark', $event)"
                        />
                        <BaseCheckbox
                            v-model="showInReportSheetStates.show_subject_position"
                            name="showInReportSheet_show_subject_position"
                            :label="$trans('exam.config.props.show_subject_position')"
                            @update:modelValue="updateShowInReportSheet('show_subject_position', $event)"
                        />
                        <BaseCheckbox
                            v-model="showInReportSheetStates.show_course_position"
                            name="showInReportSheet_show_course_position"
                            :label="$trans('exam.config.props.show_course_position')"
                            @update:modelValue="updateShowInReportSheet('show_course_position', $event)"
                        />
                    </div>
                </div>
                <div class="col-span-3 sm:col-span-1">
                    <BaseInput
                        type="text"
                        v-model="form.commentLabel"
                        name="commentLabel"
                        :label="$trans('exam.config.props.comment_label')"
                        :placeholder="$trans('exam.comment')"
                        v-model:error="formErrors.commentLabel"
                    />
                </div>
                <div class="col-span-3 sm:col-span-1">
                    <BaseInput
                        type="text"
                        v-model="form.commentBehaviouralLabel"
                        name="commentBehaviouralLabel"
                        :label="$trans('exam.config.props.comment_behavioural_label')"
                        :placeholder="$trans('exam.comment_behavioural')"
                        v-model:error="formErrors.commentBehaviouralLabel"
                    />
                </div>
            </div>
        </FormAction>
    </ParentTransition>
</template>

<script>
export default {
    name: "ExamConfigGeneral",
}
</script>

<script setup>
import { inject, reactive, watch, onMounted } from "vue"
import { useRoute } from "vue-router"
import { getFormErrors } from "@core/helpers/action"

const route = useRoute()

const $trans = inject("$trans")

const initUrl = "config/"
const formErrors = getFormErrors(initUrl)

const preRequisites = reactive({})

const initForm = {
    marksheetFormat: "",
    type: "exam",
    visibleMarksheetTypes: ["cumulative", "term_wise", "exam_wise_default"],
    showInReportSheet: [],
    commentLabel: "",
    commentBehaviouralLabel: "",
}

const form = reactive({ ...initForm })

// Create a reactive object to track the state of each checkbox
const checkboxStates = reactive({
    cumulative: false,
    term_wise: false,
    exam_wise_credit_based: false,
    exam_wise: false,
    exam_wise_default: false,
})

// Create a reactive object to track the state of show in report sheet checkboxes
const showInReportSheetStates = reactive({
    show_class_average_mark: false,
    show_class_highest_mark: false,
    show_class_lowest_mark: false,
    show_subject_position: false,
    show_course_position: false,
})

// Initialize checkbox states based on the form data
const initCheckboxStates = () => {
    const types = form.visibleMarksheetTypes || []
    checkboxStates.cumulative = types.includes('cumulative')
    checkboxStates.term_wise = types.includes('term_wise')
    checkboxStates.exam_wise_credit_based = types.includes('exam_wise_credit_based')
    checkboxStates.exam_wise = types.includes('exam_wise')
    checkboxStates.exam_wise_default = types.includes('exam_wise_default')

    const showInReportSheet = form.showInReportSheet || []
    showInReportSheetStates.show_class_average_mark = showInReportSheet.includes('show_class_average_mark')
    showInReportSheetStates.show_class_highest_mark = showInReportSheet.includes('show_class_highest_mark')
    showInReportSheetStates.show_class_lowest_mark = showInReportSheet.includes('show_class_lowest_mark')
    showInReportSheetStates.show_subject_position = showInReportSheet.includes('show_subject_position')
    showInReportSheetStates.show_course_position = showInReportSheet.includes('show_course_position')
}

// Update the form's visibleMarksheetTypes array when a checkbox changes
const updateVisibleMarksheetTypes = (type, checked) => {
    if (checked) {
        // Add the type if it's not already in the array
        if (!form.visibleMarksheetTypes.includes(type)) {
            form.visibleMarksheetTypes.push(type)
        }
    } else {
        // Remove the type from the array
        const index = form.visibleMarksheetTypes.indexOf(type)
        if (index !== -1) {
            form.visibleMarksheetTypes.splice(index, 1)
        }
    }
}

// Update the form's showInReportSheet array when a checkbox changes
const updateShowInReportSheet = (type, checked) => {
    if (checked) {
        // Add the type if it's not already in the array
        if (!form.showInReportSheet.includes(type)) {
            form.showInReportSheet.push(type)
        }
    } else {
        // Remove the type from the array
        const index = form.showInReportSheet.indexOf(type)
        if (index !== -1) {
            form.showInReportSheet.splice(index, 1)
        }
    }
}

// Watch for changes in the form data and update checkbox states
watch(() => form.visibleMarksheetTypes, () => {
    initCheckboxStates()
}, { deep: true })

watch(() => form.showInReportSheet, () => {
    initCheckboxStates()
}, { deep: true })

// Initialize checkbox states when component is mounted
onMounted(() => {
    initCheckboxStates()
})

const setPreRequisites = () => {
    // Initialize checkbox states when data is loaded
    initCheckboxStates()
}
</script>
