# Report Sheet Configuration Implementation

This document outlines the implementation of the new report sheet configuration features as requested.

## Overview

The implementation adds four major features to the report sheet system:

1. **"Show in Report Sheet" Configuration** - Allows schools to control which additional columns are displayed
2. **New Display Columns** - Adds highest mark, lowest mark, average mark, and position columns
3. **Custom Comment Field Labels** - Allows schools to customize comment field labels
4. **Improved Comment Table Layout** - Better visual presentation of comments in report sheets

## 1. Show in Report Sheet Configuration

### Backend Changes

**File: `app/Actions/Config/Module/StoreExamConfig.php`**
- Added validation for new configuration fields:
  - `show_in_report_sheet` (array of options)
  - `comment_label` (string, max 100 chars)
  - `comment_behavioural_label` (string, max 100 chars)
- Added default empty array for `show_in_report_sheet` when not provided

**File: `resources/var/config.json`**
- Added default configuration values:
  ```json
  {
      "name": "show_in_report_sheet",
      "value": []
  },
  {
      "name": "comment_label",
      "value": ""
  },
  {
      "name": "comment_behavioural_label",
      "value": ""
  }
  ```

### Frontend Changes

**File: `resources/js/views/Pages/Exam/Config/General.vue`**
- Added new form fields for report sheet configuration
- Added reactive checkbox states for all five options:
  - Show Class Average Mark
  - Show Class Highest Mark
  - Show Class Lowest Mark
  - Show Subject Position
  - Show Course Position
- Added input fields for custom comment labels
- Implemented proper state management and validation

### Translation Keys

**File: `lang/en/exam.php`**
- Added translation keys for all new configuration options
- Added keys for highest, lowest, average, and course position

## 2. New Display Columns Implementation

### Mark Summary Service

**File: `app/Services/Exam/Report/MarkSummaryService.php`**
- Modified to conditionally show summary rows based on configuration
- Added course position row at the bottom when enabled
- Updated subject position calculations to be conditional
- Modified header generation to include new columns only when enabled

### Cumulative Marksheet Service

**File: `app/Services/Exam/CumulativeMarksheetService.php`**
- Added configuration checks for new columns
- Modified header generation to include new columns conditionally
- Updated colspan calculations to account for new columns
- Added course position row functionality

## 3. Custom Comment Field Labels

### Template Updates

**Files Modified:**
- `resources/views/print/exam/cumulative-marksheet.blade.php`
- `resources/views/print/exam/exam-wise-marksheet.blade.php`

**Implementation:**
- Uses `config('config.exam.comment_label') ?: trans('exam.comment')` pattern
- Falls back to default translations when custom labels are not set
- Applies to both comment and comment_behavioural fields

## 4. Improved Comment Table Layout

### Template Changes

**File: `resources/views/print/exam/cumulative-marksheet.blade.php`**
- Replaced side-by-side layout with proper table structure
- Added larger row and column spans for better presentation
- Implemented nested table structure for better organization
- Added proper styling with borders and padding

**New Structure:**
```html
<table class="mt-4 inner-table" width="100%" border="1">
    <tr>
        <td colspan="2" style="padding: 10px;">
            <table width="100%" border="0">
                <tr>
                    <td style="width: 60%; vertical-align: top;">
                        <table width="100%" border="1" class="inner-table">
                            <!-- Comment fields with proper headers and content areas -->
                        </table>
                    </td>
                    <td style="width: 40%; vertical-align: top;">
                        <!-- Grade details section -->
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
```

## Configuration Options

The new "Show in Report Sheet" configuration supports these options:

1. **show_class_average_mark** - Shows average marks for each subject
2. **show_class_highest_mark** - Shows highest marks for each subject  
3. **show_class_lowest_mark** - Shows lowest marks for each subject
4. **show_subject_position** - Shows student position in each subject
5. **show_course_position** - Shows overall course position

All options default to `false` (not shown) to maintain backward compatibility.

## Usage

### Admin Configuration
1. Navigate to Exam Configuration
2. Find "Show in Report Sheet" section
3. Select desired options using checkboxes
4. Set custom comment labels if needed
5. Save configuration

### Report Generation
- New columns appear automatically based on configuration
- Calculations are performed using existing logic from MarkSummaryService
- Position calculations use total marks for ranking
- Course position appears at bottom of report sheets

## Testing

A comprehensive test suite has been created in `tests/Feature/ReportSheetConfigTest.php` to verify:
- Configuration validation
- Default value handling
- Field persistence
- Error handling for invalid options

## Backward Compatibility

All changes maintain full backward compatibility:
- Existing report sheets continue to work unchanged
- New features are opt-in via configuration
- Default values preserve current behavior
- No breaking changes to existing APIs

## Files Modified

### Backend
- `app/Actions/Config/Module/StoreExamConfig.php`
- `app/Services/Exam/Report/MarkSummaryService.php`
- `app/Services/Exam/CumulativeMarksheetService.php`
- `resources/var/config.json`
- `lang/en/exam.php`

### Frontend
- `resources/js/views/Pages/Exam/Config/General.vue`

### Templates
- `resources/views/print/exam/cumulative-marksheet.blade.php`
- `resources/views/print/exam/exam-wise-marksheet.blade.php`

### Tests
- `tests/Feature/ReportSheetConfigTest.php`

## Recent Fixes (Latest Update)

### Issue 1: Empty Columns Fixed ✅
**Problem**: New columns (highest, lowest, average, position) were showing but empty
**Solution**:
- Added `addSubjectStatistics()` method to `CumulativeMarksheetService.php`
- Implemented proper calculation logic similar to `MarkSummaryService.php`
- Added correct keys to column data for identification during calculation
- Statistics are calculated from actual student total marks per subject

**Calculation Logic**:
- **Highest Mark**: `max()` of all student totals for each subject
- **Lowest Mark**: `min()` of all student totals for each subject
- **Average Mark**: `round(sum/count, 2)` of all student totals for each subject
- **Position**: Ranked by total marks in descending order with proper tie handling

### Issue 2: Comment Layout Separation Fixed ✅
**Problem**: Comments and exam grade were in the same row/column
**Solution**:
- Completely separated comment section from grade details
- Comments now take full width in their own table
- Grade details appear in a separate table below comments
- Increased padding and minimum heights for better presentation

**New Layout Structure**:
```html
<!-- Comments Section - Full Width -->
<table class="mt-4 inner-table" width="100%" border="1">
    <!-- Comment Behavioural -->
    <!-- Comment -->
</table>

<!-- Grade Details Section - Separate Table Below -->
<table class="mt-4 inner-table" width="100%" border="1">
    <!-- Grade table with proper headers -->
</table>
```

## Implementation Details

### Files Modified in Latest Update:
1. **`app/Services/Exam/CumulativeMarksheetService.php`**:
   - Added `addSubjectStatistics()` method (lines 1556-1664)
   - Updated column generation with proper keys
   - Added header generation for new columns
   - Updated colspan calculations

2. **`resources/views/print/exam/cumulative-marksheet.blade.php`**:
   - Separated comments from grade details completely
   - Improved table structure and styling
   - Added proper headers for grade table

### Calculation Process:
1. **Data Collection**: Collects all student total marks per subject
2. **Statistics Calculation**: Computes highest, lowest, average for each subject
3. **Position Calculation**: Ranks students by total marks with tie handling
4. **Data Update**: Updates the row data with calculated values using proper keys

### Key Features:
- **Conditional Display**: Only calculates and shows enabled columns
- **Proper Tie Handling**: Students with same marks get same position
- **Robust Filtering**: Ignores invalid marks (NA, 0, non-numeric)
- **Performance Optimized**: Only processes when columns are enabled

## Next Steps

1. Test the implementation thoroughly with real data
2. Verify all marksheet formats work correctly
3. Ensure proper calculation of statistics
4. Test with different tenant configurations
5. Validate print layouts and formatting
6. Test position calculations with tied students
7. Verify comment layout separation in all browsers
