<?php

namespace Tests\Feature;

use App\Actions\Config\Module\StoreExamConfig;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Request;
use Tests\TestCase;

class ReportSheetConfigTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test the StoreExamConfig action with new report sheet configuration
     */
    public function test_store_exam_config_with_report_sheet_options(): void
    {
        // Create a mock request with the new configuration options
        $requestData = [
            'marksheet_format' => 'School',
            'visible_marksheet_types' => ['cumulative', 'term_wise', 'exam_wise_default'],
            'show_in_report_sheet' => ['show_class_average_mark', 'show_subject_position'],
            'comment_label' => 'Teacher Comments',
            'comment_behavioural_label' => 'Behavioral Assessment'
        ];

        // Mock the request
        $request = Request::create('/', 'POST', $requestData);
        app()->instance('request', $request);

        // Test the action
        $result = StoreExamConfig::handle();

        // Assert the result contains our new fields
        $this->assertArrayHasKey('show_in_report_sheet', $result);
        $this->assertArrayHasKey('comment_label', $result);
        $this->assertArrayHasKey('comment_behavioural_label', $result);

        // Assert the values are correct
        $this->assertEquals(['show_class_average_mark', 'show_subject_position'], $result['show_in_report_sheet']);
        $this->assertEquals('Teacher Comments', $result['comment_label']);
        $this->assertEquals('Behavioral Assessment', $result['comment_behavioural_label']);
    }

    /**
     * Test validation of show_in_report_sheet field
     */
    public function test_show_in_report_sheet_validation(): void
    {
        $requestData = [
            'marksheet_format' => 'School',
            'show_in_report_sheet' => ['invalid_option'],
        ];

        $request = Request::create('/', 'POST', $requestData);
        app()->instance('request', $request);

        $this->expectException(\Illuminate\Validation\ValidationException::class);
        StoreExamConfig::handle();
    }

    /**
     * Test default values when fields are not provided
     */
    public function test_default_values_when_fields_not_provided(): void
    {
        $requestData = [
            'marksheet_format' => 'School',
        ];

        $request = Request::create('/', 'POST', $requestData);
        app()->instance('request', $request);

        $result = StoreExamConfig::handle();

        // Assert default values
        $this->assertEquals([], $result['show_in_report_sheet']);
        $this->assertEquals(['cumulative', 'term_wise', 'exam_wise_default'], $result['visible_marksheet_types']);
    }
}
