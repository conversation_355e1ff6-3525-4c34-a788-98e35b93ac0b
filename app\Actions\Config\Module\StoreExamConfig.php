<?php

namespace App\Actions\Config\Module;

class StoreExamConfig
{
    public static function handle(): array
    {
        $input = request()->validate([
            'marksheet_format' => 'sometimes|string|max:50|in:India,School,Cameroon',
            'visible_marksheet_types' => 'sometimes|array',
            'visible_marksheet_types.*' => 'sometimes|string|in:cumulative,term_wise,exam_wise_credit_based,exam_wise,exam_wise_default',
            'show_in_report_sheet' => 'sometimes|array',
            'show_in_report_sheet.*' => 'sometimes|string|in:show_class_average_mark,show_class_highest_mark,show_class_lowest_mark,show_subject_position,show_course_position',
            'comment_label' => 'sometimes|string|max:100',
            'comment_behavioural_label' => 'sometimes|string|max:100',
        ], [], [
            'marksheet_format' => __('exam.config.props.marksheet_format'),
            'visible_marksheet_types' => __('exam.config.props.visible_marksheet_types'),
            'show_in_report_sheet' => __('exam.config.props.show_in_report_sheet'),
            'comment_label' => __('exam.config.props.comment_label'),
            'comment_behavioural_label' => __('exam.config.props.comment_behavioural_label'),
        ]);

        // Set default visible marksheet types if not provided
        if (!isset($input['visible_marksheet_types'])) {
            $input['visible_marksheet_types'] = ['cumulative', 'term_wise', 'exam_wise_default'];
        }

        // Set default show in report sheet options if not provided (all false by default)
        if (!isset($input['show_in_report_sheet'])) {
            $input['show_in_report_sheet'] = [];
        }

        return $input;
    }
}
